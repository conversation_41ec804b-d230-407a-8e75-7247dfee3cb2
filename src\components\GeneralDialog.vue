<template>
  <el-dialog
    v-if="dialogVisible"
    :before-close="handleCancel"
    :class="{ 'overflow-show': generalDialogTitle }"
    :destroy-on-close="true"
    :title="title"
    :top="dialogTop"
    :visible.sync="dialogVisible"
    :width="dialogWidth"
    append-to-body
    class="general-dialog"
  >
    <template v-if="showHeader" slot="title"></template>

    <!-- 默认内容插槽 -->
    <span v-if="generalDialogTitle" class="general-dialog-title">
      {{ generalDialogTitle }}
      <!-- Header右侧按钮 -->
      <div class="header-buttons">
        <el-button
          type="primary"
          size="small"
          @click="handleQuickResponse"
          class="header-btn"
        >
          一键响应
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="handleGenerateReport"
          class="header-btn"
        >
          生成要情
        </el-button>
      </div>
    </span>
    <div :style="generalDialogTitle ? { 'margin-top': '55px' } : {}">
      <slot></slot>
    </div>
    <span v-if="showFooter" slot="footer" class="dialog-footer">
      <el-button v-if="showCancel" @click="handleCancel">取 消</el-button>
      <el-button v-if="showConfirm" type="primary" @click="handleConfirm"
        >确 定</el-button
      >
      <el-button
        v-for="item in buttonList"
        :type="item.type"
        :key="item.id"
        :style="{ width: item.width || '100px' }"
        @click="handleButtonClick(item)"
      >
        {{ item.text }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "GeneralDialog",
  props: {
    dialogWidth: {
      type: String,
      default: "90%",
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },

    dialogTop: {
      type: String,
      default: "60px",
    },

    title: {
      type: String,
      default: "",
    },
    generalDialogTitle: {
      type: String,
      default: "",
    },
    content: {
      type: String,
      default: "",
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    showConfirm: {
      type: Boolean,
      default: true,
    },

    //父级组件可以不传，子级组件必传，这个字段控制弹窗在最后打开时，清空组件名称
    setComponentName: {
      type: String,
      default: "",
    },
    buttonList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    handleConfirm() {
      this.$emit("confirm");
    },

    handleCancel() {
      this.$emit("cancel");
      // //清空组件名称
      // this.$store.commit("setComponentName", this.setComponentName);
    },
    handleButtonClick(item) {
      this.$emit("buttonClick", item);
    },
    handleQuickResponse() {
      this.$emit("quickResponse");
    },
    handleGenerateReport() {
      this.$emit("generateReport");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.general-dialog {
  ::v-deep .el-dialog {
    max-height: 90%;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    border-radius: $radiusTwenty;

    &__body {
      flex: 1;
      overflow: hidden;
      min-height: $tabsAndDialogMinHeight;
      padding: 0 !important;
    }
  }
}

.overflow-show {
  ::v-deep .el-dialog {
    &__body {
      flex: 1;
      overflow: auto;
      min-height: 190px;
      max-height: calc(100vh - 115px);
      padding: 0 !important;
    }
  }
}

::v-deep {
  .el-input__inner {
    border-radius: $radiusFive;
  }

  .el-input__inner:focus {
    border-color: var(--themeColor) !important;
  }
}

.general-dialog-title {
  z-index: 1;
  position: absolute;
  width: calc(100% - 120px);
  height: 70px;
  line-height: 85px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: $dialogTitleColor;
  font-size: 20px;
  font-weight: 700;
  margin-left: 60px;
  margin-bottom: $radiusTen;
  background-color: var(--white);

  .header-buttons {
    display: flex;
    gap: 10px;

    .header-btn {
      height: 32px;
      padding: 0 16px;
      font-size: 14px;
      border-radius: 4px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: $radiusTwenty;

  ::v-deep {
    .el-button {
      width: 100px;
      height: 40px;
      border-color: var(--themeColor);
    }

    .el-button:hover,
    .el-button:focus {
      color: var(--themeColor);
      border-color: var(--themeColor);
      background-color: var(--lightGray1);
    }

    .el-button--primary,
    .el-button--primary:hover,
    .el-button--primary:focus {
      color: var(--white);
      background-color: var(--themeColor);
      border-color: var(--themeColor);
    }
  }
}

::v-deep .el-dialog__header {
  padding: 0;
  border-bottom-width: 0;

  .el-dialog__headerbtn {
    z-index: 1000;
    width: 44px;
    height: 44px;

    background: var(--lightGray1);
    border-radius: 14px;

    .el-dialog__close {
      color: var(--themeColor);
      font-weight: bold;
    }
  }
}
</style>
